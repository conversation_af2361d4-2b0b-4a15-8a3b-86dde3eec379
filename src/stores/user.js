import { getAllUserInfo, logout as logoutApi } from '@/apis/common'
import { removeToken, ssoLoginUrl } from '@/utils/config.js'
import { isEmpty } from '@/utils/is'
import { isValidMenuItem } from '@/utils/menu'
import TreeUtils from '@/utils/tree'

// 初始化state
const initState = {
  // 用户信息
  userInfo: {},
  // 用户角色列表
  userRoles: [],
  // 用户菜单数据（包含菜单和按钮）
  userMenu: [],
  // 用户权限
  userPermissions: [],
}

const useUserStore = defineStore('user', {
  state: () => ({
    ...initState,
  }),

  getters: {
    // 角色标识数组
    roleCodes: (state) => {
      return state.userRoles.map((item) => item.roleCode)
    },
    // 用户手机号
    phone: (state) => {
      return (state.userInfo?.phone || '').replace('+86', '')
    },
    // 判断用户是否已登录并拥有用户信息
    isLoggedIn: (state) => !isEmpty(state.userInfo) && !isEmpty(state.userRoles),

    // 侧边菜单 去除按钮和隐藏菜单
    siderMenu: (state) => {
      const formatMenuTree = (menuTree) => {
        if (!Array.isArray(menuTree)) {
          menuTree = [menuTree]
        }

        return menuTree.filter(isValidMenuItem).map((menuItem) => {
          const formattedMenu = {
            id: menuItem.id,
            key: menuItem.path || menuItem.id,
            label: menuItem.name,
            icon: menuItem.meta?.icon + ' iconfont' || '',
            meta: menuItem.meta,
          }

          if (menuItem.children && menuItem.children.length > 0) {
            formattedMenu.children = formatMenuTree(menuItem.children)
          }

          return formattedMenu
        })
      }

      return formatMenuTree(state.userMenu)
    },
    // 取菜单第一项叶子节点
    firstMenu: (state) => {
      const menuTree = state.userMenu
      return (
        TreeUtils.findNode(menuTree || [], (node) => node.path !== '' && !node?.children?.length)
          ?.path || ''
      )
    },
  },

  actions: {
    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = userInfo
    },

    // 设置用户角色
    setUserRoles(userRoles) {
      let permissions = []

      // 合并用户权限
      if (!isEmpty(userRoles)) {
        userRoles.forEach((item) => {
          permissions = item?.permission ? item?.permission?.concat(permissions) : permissions
        })
      }
      this.userRoles = userRoles
      this.userPermissions = permissions
    },

    // 设置用户菜单
    setUserMenu(userMenu) {
      this.userMenu = userMenu
    },

    // 获取所有用户信息
    async fetchAllUserInfo() {
      try {
        const [userInfo, userRoles, userMenu] = await getAllUserInfo()
        this.setUserInfo(userInfo)
        this.setUserRoles(userRoles)
        this.setUserMenu(userMenu)
        return { userInfo, userRoles, userMenu }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      }
    },

    // 检查用户权限
    hasPermission(permission) {
      if (!permission) return true
      return this.userPermissions.includes(permission)
    },

    // 检查用户角色
    hasRole(role) {
      if (!role) return true
      return this.userRoles.some(
        (userRole) => userRole.roleCode === role || userRole.roleName === role,
      )
    },

    // 清空信息
    clearUserStore() {
      this.$patch(initState)
      removeToken()
    },

    // 退出登录
    async logout() {
      try {
        await logoutApi()
      } catch (error) {
        console.error('退出登录接口调用失败:', error)
      } finally {
        this.clearUserStore()
        window.location.href = ssoLoginUrl
      }
    },
  },

  // 持久化配置
  persist: true,
})

export default useUserStore
