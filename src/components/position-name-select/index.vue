<template>
  <ElSelect
    v-model="positionName"
    placeholder="请输入职位名称"
    :disabled="disabled"
    :filterable="true"
    :remote="true"
    :clearable="true"
    :remote-method="getPositionNameOptions">
    <ElOption
      v-for="item in positionNameOptions"
      :key="item.positionName"
      :label="item.positionName"
      :value="item.positionName" />
  </ElSelect>
</template>

<script setup>
  import { getRelatedJobList } from '@/apis/job'

  const positionName = defineModel('positionName', {
    type: String,
    default: '',
  })

  const { disabled } = defineProps({
    disabled: {
      type: Boolean,
      default: false,
    },
  })
  const positionNameOptions = ref([])
  function getPositionNameOptions(query) {
    if (!query) {
      positionNameOptions.value = []
      return
    }
    getRelatedJobList({
      positionName: query,
    }).then((data) => {
      positionNameOptions.value = data
    })
  }
</script>

<style lang="scss" scoped></style>
