<template>
  <ElDialog
    v-model="visible"
    title="选择职位"
    width="1080px"
    :append-to-body="true"
    @open="handleOpen">
    <slot name="alert"></slot>
    <ElTable
      v-loading="tableLoading"
      :max-height="465"
      :border="true"
      :data="tableData">
      <template #empty>
        <ElEmpty
          :image-size="160"
          description="暂无职位信息" />
      </template>
      <ElTableColumn
        label="职位ID"
        prop="bid"
        fixed="left"
        width="200px" />
      <ElTableColumn
        label="职位名称"
        prop="positionName"
        fixed="left"
        width="140px"
        :show-overflow-tooltip="true" />
      <ElTableColumn
        label="职位类型"
        width="100px"
        prop="positionType">
        <template #default="{ row }">
          {{ getDictLabel('JOB_TYPE', row.positionType) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="职位状态"
        width="100px"
        prop="status">
        <ElTag type="success">已发布</ElTag>
      </ElTableColumn>
      <ElTableColumn
        label="发布渠道"
        width="100px"
        prop="releaseChannel">
        <template #default="{ row }">
          <!-- 发布过的职位显示 “Boss直聘” -->
          {{ row.status === '已发布' || row.status === '已关闭' ? 'BOSS直聘' : '-' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="关联简历数"
        width="100px"
        prop="resumeCount">
        <template #default="{ row }">
          <ElButton
            :link="true"
            type="info">
            {{ row.resumeCount }}
          </ElButton>
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="所属部门"
        width="180px"
        prop="deptName" />
      <ElTableColumn
        label="最新发布时间"
        width="180px"
        prop="releaseTime" />
      <ElTableColumn
        label="操作"
        width="100px"
        fixed="right">
        <template #default="{ row }">
          <ElButton
            :link="true"
            type="primary"
            @click="handleSelect(row)">
            选择
          </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
    <template #footer>
      <div class="flex justify-end">
        <ElPagination
          v-model:page-size="paginationData.pageSize"
          v-model:current-page="paginationData.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          layout="total,sizes,prev,pager,next,jumper"
          :total="paginationData.total"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentPageChange" />
      </div>
    </template>
  </ElDialog>
</template>

<script setup>
  import { getJobList } from '@/apis/job'
  import { getDictLabel } from '@/stores/dict'

  const props = defineProps({
    api: {
      type: Function,
      default: getJobList,
    },
  })

  const model = defineModel({
    type: Object,
    default: () => ({}),
  })

  const visible = defineModel('visible', {
    type: Boolean,
    default: false,
  })

  const emit = defineEmits(['confirm'])

  const tableData = ref([])
  const tableLoading = ref(false)

  function getTableData() {
    tableLoading.value = true
    props
      .api({
        status: '已发布',
        pageNum: paginationData.pageNum,
        pageSize: paginationData.pageSize,
      })
      .then((data) => {
        tableData.value = data.list
        paginationData.total = data.total
        paginationData.pageNum = data.page
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        tableLoading.value = false
      })
  }

  function handleSelect(row) {
    emit('confirm', row)
    model.value = row
  }

  function handleOpen() {
    getTableData()
  }

  const paginationData = reactive({
    pageSize: 10,
    pageNum: 1,
    total: 0,
  })
  // 改变每页条数
  function handlePageSizeChange() {
    paginationData.pageNum = 1
    getTableData()
  }
  // 改变当前页
  function handleCurrentPageChange() {
    getTableData()
  }
</script>

<style lang="scss" scoped></style>
