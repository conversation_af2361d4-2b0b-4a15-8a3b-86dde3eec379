import useCommonStore from '@/stores/common'
import useUserStore from '@/stores/user'
import auth from '@/utils/auth'
import { getToken, setToken } from '@/utils/config'
import { ssoLoginUrl } from '@/utils/config.js'
import { isEmpty } from '@/utils/is'
import NProgress from 'nprogress'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: () => import('@/layout/index.vue'),
      name: 'root',
      meta: {
        title: '首页',
        requireAuth: true,
      },
      children: [
        // 内嵌iframe
        {
          path: 'iframe',
          name: 'iframe',
          component: () => import('@/views/iframe/index.vue'),
          meta: {
            title: '',
            isIframe: true,
          },
        },
        // 首页
        {
          path: 'home',
          name: 'home',
          component: () => import('@/views/home/<USER>'),
          meta: {
            title: '首页',
            permissions: ['system:home'],
          },
        },
        // 职位管理
        {
          path: 'jobs',
          meta: {
            title: '职位管理',
            permissions: ['system:jobs'],
          },
          children: [
            {
              path: '',
              name: 'jobs',
              component: () => import('@/views/jobs/index.vue'),
              meta: {
                title: '职位管理',
                permissions: ['system:jobs'],
              },
            },
            // 新增职位
            {
              path: 'create',
              component: () => import('@/views/jobs/form/index.vue'),
              meta: {
                title: '新增职位',
                permissions: ['system:jobs:create'],
                activeMenu: '/jobs',
                formType: 'create',
              },
            },
            // 编辑职位
            {
              path: 'edit',
              component: () => import('@/views/jobs/form/index.vue'),
              meta: {
                title: '编辑职位',
                permissions: ['system:jobs:edit'],
                activeMenu: '/jobs',
                formType: 'edit',
              },
            },
            // 查看职位
            {
              path: 'detail',
              component: () => import('@/views/jobs/detail/index.vue'),
              meta: {
                title: '查看职位',
                permissions: ['system:jobs:detail'],
                activeMenu: '/jobs',
              },
            },
          ],
        },
        // 打招呼管理
        {
          path: 'greet',
          meta: {
            title: '打招呼管理',
            permissions: ['system:greet'],
          },
          children: [
            // 列表
            {
              path: '',
              component: () => import('@/views/greet/index.vue'),
              meta: {
                title: '打招呼管理',
                permissions: ['system:greet'],
              },
            },
            // 匹配详情
            {
              path: 'detail/:pid/:bid',
              component: () => import('@/views/greet/detail/index.vue'),
              meta: {
                title: '匹配详情',
                // permissions: ['system:greet:detail'],
                activeMenu: '/greet',
              },
            },
          ],
        },
        // 简历管理
        {
          path: 'resume',
          name: 'resume',
          meta: {
            title: '简历管理',
            permissions: ['system:resume'],
          },
          children: [
            // 简历解析
            {
              path: 'analysis',
              component: () => import('@/views/resume/analysis/index.vue'),
              meta: {
                title: '简历解析',
                permissions: ['system:resume:analysis'],
              },
            },
            // 二级解析列表
            {
              path: 'analysis/zip',
              component: () => import('@/views/resume/analysis/zip/index.vue'),
              meta: {
                title: '二级解析列表',
                permissions: ['system:resume:analysis:zip'],
              },
            },
            // 简历解析详情
            {
              path: 'analysis/info',
              component: () => import('@/views/resume/analysis/info/index.vue'),
              meta: {
                title: '简历解析详情',
                permissions: ['system:resume:analysis:info'],
              },
            },
            // 人才画像
            {
              path: 'talents',
              name: 'talents',
              component: () => import('@/views/resume/talents/index.vue'),
              meta: {
                title: '人才画像',
                permissions: ['system:resume:talents'],
              },
            },
            {
              path: 'talents/detail',
              name: 'talents/detail',
              component: () => import('@/views/resume/talents/detail/index.vue'),
              meta: {
                title: '人才画像详情',
                permissions: ['system:resume:talents:detail'],
              },
            },
          ],
        },
        // 标签管理
        {
          path: 'tag',
          name: 'tag',
          component: () => import('@/views/tag/index.vue'),
          meta: {
            title: '标签管理',
            permissions: ['system:tag'],
          },
        },
        // 企业信息管理
        {
          path: 'enterprise',
          name: 'enterprise',
          component: () => import('@/views/enterprise/index.vue'),
          meta: {
            title: '企业信息管理',
            permissions: ['system:enterprise'],
          },
        },
        // 邮箱管理
        {
          path: 'email',
          name: 'email',
          component: () => import('@/views/email/index.vue'),
          meta: {
            title: '邮箱管理',
            permissions: ['system:email'],
          },
        },
        // 渠道管理
        {
          path: 'channel',
          name: 'channel',
          component: () => import('@/views/channel/index.vue'),
          meta: {
            title: '渠道管理',
            permissions: ['system:channel'],
          },
        },
      ],
    },
    // 无权限
    {
      path: '/403',
      component: () => import('@/router/PageForbidden.vue'),
      meta: {
        title: '无权限访问',
      },
    },
    // 未找到
    {
      path: '/404',
      component: () => import('@/router/PageNotFond.vue'),
      meta: {
        title: '页面不存在',
      },
    },
    // 获取信息失败
    {
      path: '/info-error',
      component: () => import('@/router/PageInfoError.vue'),
      meta: {
        title: '获取信息失败',
      },
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/404',
    },
  ],
})

// 路由前置守卫
router.beforeEach(async (to) => {
  NProgress.start()

  // 检查是否需要认证或权限验证
  const requireAuth = to.matched.some((record) => record.meta?.requireAuth)
  const requiredPermissions = to.matched
    .map((record) => record.meta?.permissions)
    .filter(Boolean)
    .flat()
  const requiredRoles = to.matched
    .map((record) => record.meta?.roles)
    .filter(Boolean)
    .flat()

  // 需要登陆或者需要权限或者需要角色
  if (requireAuth || !isEmpty(requiredPermissions) || !isEmpty(requiredRoles)) {
    const token = to.query.id_token || getToken()

    if (!token) {
      window.location.href = ssoLoginUrl
      return false
    }

    // 存在token进行设置
    setToken(token)

    // 获取用户store实例
    const userStore = useUserStore()

    try {
      // 检查用户信息是否存在，不存在则获取
      if (!userStore.isLoggedIn) {
        await userStore.fetchAllUserInfo()
      }

      // 权限验证
      if (requiredPermissions.length > 0) {
        const hasPermission = auth.hasPermiAnd(requiredPermissions)

        if (!hasPermission) {
          console.warn(
            `用户无权限访问路由: ${to.path}, 需要权限: ${requiredPermissions.join(', ')}`,
          )
          return {
            path: '/403',
            replace: true,
          }
        }
      }

      // 角色验证
      if (requiredRoles.length > 0) {
        const hasRole = auth.hasRoleAnd(requiredRoles)

        if (!hasRole) {
          console.warn(`用户无角色权限访问路由: ${to.path}, 需要角色: ${requiredRoles.join(', ')}`)
          return {
            path: '/403',
            replace: true,
          }
        }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 进入到失败界面
      return {
        path: '/info-error',
        replace: true,
      }
    }

    // 默认路由 取路由表第一项
    if (to.name === 'root') {
      const path = userStore.firstMenu

      // 没有任何路由权限
      if (!path) {
        return {
          path: '/403',
          replace: true,
        }
      }

      return {
        path: path,
        replace: true,
      }
    }
  }
})

router.afterEach((to) => {
  const commonStore = useCommonStore()
  commonStore.matched = to.matched
  NProgress.done()
})

export default router
