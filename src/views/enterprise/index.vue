<template>
  <div class="enterprise">
    <p class="mt-[180px] text-[36px] leading-[44px] font-[600] text-[#303133]">
      欢迎使用 企业信息管理
    </p>
    <p class="mt-[24px] text-center text-[16px] leading-[24px] text-[#606266]">
      AI生成职位JD若需关联企业信息，则点击“上传文件”按钮前往 Fastgpt
      知识库上传，具体操作可点击“下载操作文件”按钮查看。
    </p>
    <p class="text-center text-[16px] leading-[24px] text-[#606266]">
      请根据操作文件指引内容进行操作，切勿在 Fastgpt 内随意操作其他功能，否则将会导致AI功能报错！
    </p>
    <div class="mt-[48px] flex gap-[36px]">
      <div
        class="option"
        @mouseenter="handleMouseEnter('fastgpt')"
        @mouseleave="handleMouseLeave">
        <div class="option_icon">
          <img
            class="option_icon_img"
            src="@/assets/images/enterprise/icon_fastgpt.png"
            alt="" />
        </div>
        <p class="option_text">Fastgpt</p>
        <ElButton
          :type="enterItem === 'fastgpt' ? 'primary' : 'default'"
          :circle="true"
          size="large"
          @click="handleFastgpt">
          <template #icon>
            <i class="iconfont icon-right" />
          </template>
        </ElButton>
      </div>
      <div
        class="option"
        @mouseenter="handleMouseEnter('doc')"
        @mouseleave="handleMouseLeave">
        <div class="option_icon">
          <img
            class="option_icon_img"
            src="@/assets/images/enterprise/icon_doc.png"
            alt="" />
        </div>
        <p class="option_text">操作手册</p>
        <ElButton
          :type="enterItem === 'doc' ? 'success' : 'default'"
          :circle="true"
          size="large"
          @click="handleDownload">
          <template #icon>
            <i class="iconfont icon-download" />
          </template>
        </ElButton>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { downloadOperationFile } from '@/apis/enterprise'
  import { downloadFileByBlob } from '@/utils/download'

  const enterItem = ref('')

  function handleMouseEnter(type) {
    enterItem.value = type
  }

  function handleMouseLeave() {
    enterItem.value = ''
  }

  function handleFastgpt() {
    window.open(import.meta.env.VITE_FAST_GPT_KNOWLEDGE_BASE_URL, '_blank')
  }

  function handleDownload() {
    console.log('下载操作文件')
    downloadOperationFile().then((res) => {
      downloadFileByBlob(res.data, '操作手册.pdf')
    })
  }
</script>

<style lang="scss" scoped>
  .enterprise {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow: hidden;
    background-color: #fff;
  }

  .option {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 192px;
    height: 240px;
    border: 1px solid rgba(0, 85, 255, 0.1);
    border-radius: 8px;
    background: #ffffff;

    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow:
        0px 12px 32px 4px rgba(0, 0, 0, 0.04),
        0px 8px 20px 0px rgba(0, 0, 0, 0.08);
    }

    &_icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 68px;
      height: 68px;
      margin-top: 20px;
      border: 1px solid rgba($color: #0055ff, $alpha: 0.06);
      border-radius: 50%;
      background-color: rgba($color: #0055ff, $alpha: 0.05);

      &_img {
        width: 40px;
        height: 40px;
      }
    }

    &_text {
      margin: 10px 0 38px 0;
      color: #303133;
      font-weight: 600;
      font-size: 16px;
    }
  }
</style>
