<template>
  <div class="dashboard">
    <div
      v-loading="loading.statistics"
      class="flex gap-[16px] overflow-auto">
      <div
        :style="{
          '--bg-color-from': '#e7f6ff',
          '--bg-color-to': '#FFFFFF',
          '--hover-color': '#009dff',
        }"
        class="item item_hover"
        @click="
          router.push({
            path: '/jobs',
            query: {
              status: '已发布',
            },
          })
        ">
        <p class="item_title">招聘中的职位数</p>
        <p class="item_value">{{ statisticsData.recruitmentCount }}</p>
        <img
          class="item_img"
          src="@/assets/images/home/<USER>"
          alt="" />
      </div>
      <div
        class="item"
        :style="{
          '--bg-color-from': '#E7FFF5',
          '--bg-color-to': '#FFFFFF',
        }">
        <p class="item_title">职位JD生成数</p>
        <p class="item_value">{{ statisticsData.generatedJDCount }}</p>
        <img
          class="item_img"
          src="@/assets/images/home/<USER>"
          alt="" />
      </div>
      <div
        class="item"
        :style="{
          '--bg-color-from': '#E7EAFF',
          '--bg-color-to': '#FFFFFF',
        }">
        <p class="item_title">职位发布数</p>
        <p class="item_value">{{ statisticsData.postingCount }}</p>
        <img
          class="item_img"
          src="@/assets/images/home/<USER>"
          alt="" />
      </div>
      <div
        class="item"
        :style="{
          '--bg-color-from': '#E7F6FF',
          '--bg-color-to': '#FFFFFF',
        }">
        <p class="item_title">招聘沟通数</p>
        <p class="item_value">{{ statisticsData.communicationCount }}</p>
        <img
          class="item_img"
          src="@/assets/images/home/<USER>"
          alt="" />
      </div>
      <div
        class="item"
        :style="{
          '--bg-color-from': '#E7FFF5',
          '--bg-color-to': '#FFFFFF',
        }">
        <p class="item_title">简历投递量</p>
        <p class="item_value">{{ statisticsData.resumeSubmissionCount }}</p>
        <img
          class="item_img"
          src="@/assets/images/home/<USER>"
          alt="" />
      </div>
    </div>
    <div class="mt-[16px] flex gap-[16px]">
      <div
        class="chart"
        style="flex: 460px">
        <p class="chart_title">职位发布状态分析</p>
        <div class="chart_content">
          <JobStatusChart
            :loading="loading.jobStatus"
            :data="jobStatusData" />
        </div>
      </div>
      <div
        class="chart"
        style="flex: 1212px">
        <p class="chart_title">简历投递趋势</p>
        <div class="chart_content">
          <ResumeTrendChart
            :loading="loading.resumeTrend"
            :data="resumeTrendData" />
        </div>
      </div>
    </div>
    <div class="mt-[16px] flex gap-[16px]">
      <div
        class="chart"
        style="flex: 1068px">
        <p class="chart_title">沟通与简历获取分布</p>
        <div class="chart_content">
          <CommunicationChart
            :loading="loading.communication"
            :data="communicationData" />
        </div>
      </div>
      <div
        class="chart"
        style="flex: 604px">
        <p class="chart_title">打招呼获取简历转化率</p>
        <div class="chart_content">
          <TransRateChart
            :loading="loading.communication"
            :data="communicationData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    getCommunication,
    getJobStatus,
    getResumeTrend,
    getStatistics,
  } from '@/apis/home/<USER>'
  import CommunicationChart from './components/communication-chart.vue'
  import JobStatusChart from './components/job-status-chart.vue'
  import ResumeTrendChart from './components/resume-trend-chart.vue'
  import TransRateChart from './components/trans-rate-chart.vue'

  const { startDate, endDate, positionName } = defineProps({
    startDate: {
      type: String,
      default: '',
    },
    endDate: {
      type: String,
      default: '',
    },
    positionName: {
      type: String,
      default: '',
    },
  })

  const router = useRouter()

  const loading = reactive({
    statistics: false,
    jobStatus: false,
    resumeTrend: false,
    communication: false,
  })

  // 统计数据
  const statisticsData = reactive({
    recruitmentCount: 0,
    generatedJDCount: 0,
    postingCount: 0,
    communicationCount: 0,
    resumeSubmissionCount: 0,
  })
  function getStatisticsData() {
    loading.statistics = true
    getStatistics({
      startDate,
      endDate,
      positionName,
    })
      .then((data) => {
        statisticsData.recruitmentCount = data.recruitmentCount || 0
        statisticsData.generatedJDCount = data.generatedJDCount || 0
        statisticsData.postingCount = data.postingCount || 0
        statisticsData.communicationCount = data.communicationCount || 0
        statisticsData.resumeSubmissionCount = data.resumeSubmissionCount || 0
      })
      .finally(() => {
        loading.statistics = false
      })
  }

  // 职位发布状态分析
  const jobStatusData = reactive({
    // totalCount: 0,
    successCount: 0,
    failCount: 0,
    // successRate: 0,
    // failRate: 0,
  })
  function getJobStatusData() {
    loading.jobStatus = true
    getJobStatus({
      startDate,
      endDate,
      positionName,
    })
      .then((data) => {
        jobStatusData.successCount = data.successCount || 0
        jobStatusData.failCount = data.failCount || 0
      })
      .finally(() => {
        loading.jobStatus = false
      })
  }

  // 简历投递趋势
  const resumeTrendData = ref([])
  function getResumeTrendData() {
    loading.resumeTrend = true
    getResumeTrend({
      startDate,
      endDate,
      positionName,
    })
      .then((data) => {
        resumeTrendData.value = data || []
      })
      .finally(() => {
        loading.resumeTrend = false
      })
  }

  // 沟通与简历获取分布
  const communicationData = reactive({
    activeGreetCount: 0,
    passiveGreetCount: 0,
    activeResumeCount: 0,
    passiveResumeCount: 0,
  })
  function getCommunicationData() {
    loading.communication = true
    getCommunication({
      startDate,
      endDate,
      positionName,
    })
      .then((data) => {
        communicationData.activeGreetCount = data.activeGreetCount || 0
        communicationData.passiveGreetCount = data.passiveGreetCount || 0
        communicationData.activeResumeCount = data.activeResumeCount || 0
        communicationData.passiveResumeCount = data.passiveResumeCount || 0
      })
      .finally(() => {
        loading.communication = false
      })
  }

  onMounted(() => {
    getStatisticsData()
    getJobStatusData()
    getResumeTrendData()
    getCommunicationData()
  })

  watch(
    () => [startDate, endDate, positionName],
    () => {
      getStatisticsData()
      getJobStatusData()
      getResumeTrendData()
      getCommunicationData()
    },
  )
</script>

<style lang="scss" scoped>
  .item {
    position: relative;
    flex: none;
    width: 320px;
    height: 148px;
    padding: 20px;
    border: 1.5px solid #fff;
    border-radius: 4px;
    background: linear-gradient(135deg, var(--bg-color-from) 0%, var(--bg-color-to) 100%);
    transition: all ease 0.3s;

    &_hover {
      cursor: pointer;
      &:hover {
        border: 1.5px solid var(--hover-color);
      }
    }

    &_title {
      color: #606266;
      font-size: 14px;
    }

    &_value {
      color: #303133;
      font-size: 30px;
    }

    &_img {
      position: absolute;
      right: 0px;
      bottom: 0px;
    }
  }

  .chart {
    display: flex;
    flex-direction: column;
    height: 360px;
    padding: 20px;
    border-radius: 4px;
    background-color: #fff;

    &_title {
      color: #303133;
      font-weight: 600;
      font-size: 20px;
    }

    &_content {
      flex: 1;
      margin-top: 10px;
    }
  }
</style>
