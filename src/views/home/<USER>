<template>
  <div class="home">
    <div class="home_header">
      <div class="picker">
        <div
          class="picker_item"
          :class="{ active: activeTab === '招聘看板' }"
          @click="handleTabChange('招聘看板')">
          招聘看板
        </div>
        <span class="picker_separator">/</span>
        <div
          class="picker_item"
          :class="{ active: activeTab === '人才库看板' }"
          @click="handleTabChange('人才库看板')">
          人才库看板
        </div>
      </div>
      <div class="flex gap-[24px]">
        <ElDatePicker
          v-model="searchData.date"
          style="width: 280px"
          type="daterange"
          value-format="YYYY-MM-DD"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期" />
        <PositionNameSelect
          v-model:position-name="searchData.positionName"
          style="width: 280px" />
      </div>
    </div>
    <div class="home_content">
      <Recruitment
        v-if="activeTab === '招聘看板'"
        :position-name="searchData.positionName"
        :start-date="searchData?.date?.[0] || ''"
        :end-date="searchData?.date?.[1] || ''" />
      <TalentPool
        v-if="activeTab === '人才库看板'"
        :position-name="searchData.positionName"
        :start-date="searchData?.date?.[0] || ''"
        :end-date="searchData?.date?.[1] || ''" />
    </div>
  </div>
</template>

<script setup>
  import { PositionNameSelect } from '@/components'
  import Recruitment from './recruitment/index.vue'
  import TalentPool from './talent-pool/index.vue'

  const activeTab = ref('招聘看板')

  function handleTabChange(tab) {
    if (activeTab.value === tab) {
      return
    }
    activeTab.value = tab
  }

  const searchData = reactive({
    date: '',
    job: '',
  })
</script>

<style lang="scss" scoped>
  .home {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;

    &_header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 72px;
      padding: 0 20px;
      background-color: #fff;

      .picker {
        display: flex;
        align-items: center;
        gap: 12px;

        &_item {
          color: #909399;
          font-weight: 600;
          font-size: 18px;
          cursor: pointer;

          &:hover {
            color: #0055ff;
          }

          &.active {
            color: #0055ff;
          }
        }

        &_separator {
          color: #c0c4cc;
          font-size: 16px;
        }
      }
    }

    &_content {
      flex: 1;
      margin-top: 16px;
      overflow: auto;
    }
  }
</style>
