<template>
  <div
    v-loading="mapLoading"
    class="chartContainer">
    <div
      v-show="mapStack.length > 1"
      class="back"
      @click="onReturnMap">
      <ElButton
        :link="true"
        type="primary">
        返回
      </ElButton>
    </div>
    <VChart
      ref="chartRef"
      :option="option"
      class="map"
      :autoresize="true"
      @click="onClickMap" />
  </div>
</template>

<script setup>
  import { getHomeMap } from '@/apis/home/<USER>'
  import chinaJson from '@/assets/json/china.json'
  import { numberSplit } from '@/utils'
  import { geoApiUrl } from '@/utils/config'
  import axios from 'axios'
  import * as echarts from 'echarts'

  echarts.registerMap('china', chinaJson)
  const axiosInstance = axios.create({})

  const { startDate, endDate, positionName } = defineProps({
    startDate: {
      type: String,
      default: '',
    },
    endDate: {
      type: String,
      default: '',
    },
    positionName: {
      type: String,
      default: '',
    },
  })
  watch(
    () => [startDate, endDate, positionName],
    () => {
      getMapData()
    },
  )

  // 地图加载
  const mapLoading = ref(false)
  // 地图栈
  const mapStack = ref([
    {
      name: 'china', // 地图名称
      json: chinaJson, // 地图json
      code: '',
      type: '',
    },
  ])
  // 实例
  const chartRef = useTemplateRef('chartRef')

  // 获取地图数据
  const emit = defineEmits(['mapChange'])
  function getMapData() {
    mapLoading.value = true
    const params = {
      code: mapStack.value[0].code,
      type: mapStack.value[0].type,
      startDate: startDate || undefined,
      endDate: endDate || undefined,
      positionName: positionName || undefined,
    }

    getHomeMap(params)
      .then((res) => {
        const data = res.data.data
        option.series[0].data = data

        emit('mapChange', {
          name: mapStack.value[0].name,
          level: mapStack.value.length,
          data,
          params,
        })
      })
      .finally(() => {
        mapLoading.value = false
      })
  }

  onMounted(() => {
    getMapData()
  })

  function getType(level, isSecond) {
    if (isSecond) {
      return 1
    } else {
      switch (level) {
        case 2:
          return 0
        case 3:
          return 1
        default:
          return ''
      }
    }
  }
  // 点击地图
  function onClickMap(params) {
    const { name, data } = params

    if (!data?.code || data?.value <= 0) return

    const {
      properties: { adcode, childrenNum },
    } = mapStack.value[0].json.features.find((item) => {
      return item.properties.name === name
    })

    if (childrenNum > 0 && adcode) {
      // 获取下一级的地图 geoJson
      axiosInstance
        .get(`${geoApiUrl}/${adcode}.json`)
        .then(async (res) => {
          if (res.status === 200) {
            const mapJson = res.data
            // 地图入栈
            mapStack.value.unshift({
              name,
              json: mapJson,
              code: data.isSecond ? data.code + '01' : data.code,
              type: getType(mapStack.value.length + 1, data.isSecond),
            })
            echarts.registerMap(name, mapJson)

            chartRef.value.clear()
            option.series[0].map = name
            option.geo.map = name
            getMapData()
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {})
    }
  }
  // 返回上一级
  function onReturnMap() {
    mapStack.value.shift()
    const parentMap = mapStack.value[0].name

    chartRef.value.clear()
    option.series[0].map = parentMap
    option.geo.map = parentMap
    getMapData()
  }

  const option = reactive({
    geo: {
      emphasis: {
        disabled: true,
      },
      itemStyle: {
        areaColor: '#E8F3FF',
        borderWidth: 1,
        borderColor: 'rgba(196,229,255, 0.50)',
        borderType: 'solid',
      },
      map: 'china',
    },
    visualMap: {
      text: ['Max', '0'],
      max: 100,
      show: false,
      type: 'piecewise',
      pieces: [
        {
          value: 0,
          color: '#E8F3FF',
        },
        {
          min: 1,
          max: 100,
          color: '#91CBFB',
        },
        {
          min: 101,
          max: 500,
          color: '#62ACFB',
        },
        {
          min: 501,
          max: 1000,
          color: '#4E8CEC',
        },
        {
          min: 1001,
          color: '#2D5CBC',
        },
      ],
      outOfRange: {
        color: '#E8F3FF',
      },
      inRange: {
        color: ['#E8F3FF'],
      },
    },
    tooltip: {
      formatter(param) {
        return `
            <div style="display:flex;align-items:center;">
              <div style="width:6px;height:6px;border-radius:50%;background:#2D5CBC"></div>
              <div style="margin:0 20px 0 8px">${param.name}</div>
              <div>${numberSplit(param.value || 0)}人</div>
            </div>
          `
      },
    },
    series: [
      {
        type: 'map',
        map: 'china',
        center: ['50%', '50%'],
        selectedMode: false,
        // roam: true,
        geoIndex: 0,
        data: [],
      },
    ],
  })
</script>

<style lang="scss" scoped>
  .chartContainer {
    position: relative;
    flex: 1;

    .back {
      z-index: 1999;
      position: absolute;
      top: 0;
      right: 0;
    }

    .map {
      width: 100%;
      // width: 975px;
      // height: 542px;
      height: 100%;
    }
  }
</style>
