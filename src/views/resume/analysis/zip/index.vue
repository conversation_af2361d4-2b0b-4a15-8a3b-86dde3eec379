<template>
  <TablePageLayout :header-line="false">
    <template #header>
      <BackTitle title="二级解析列表" />
    </template>
    <template #search>
      <ElForm
        ref="formRef"
        :model="formData"
        :inline="true">
        <ElFormItem
          label="任务ID"
          prop="taskId">
          <ElInput
            v-model="formData.taskId"
            style="width: 300px" />
        </ElFormItem>
        <ElFormItem
          label="任务状态"
          prop="taskStatus">
          <ElSelect
            v-model="formData.taskStatus"
            style="width: 300px">
            <ElOption
              label="解析中"
              value="1" />
            <ElOption
              label="解析完成"
              value="2" />
            <ElOption
              label="解析终止"
              value="3" />
            <ElOption
              label="解析异常"
              value="4" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          label="应聘者姓名"
          prop="name">
          <ElInput
            v-model="formData.name"
            style="width: 300px" />
        </ElFormItem>
        <!-- <ElFormItem
          label="身份证号"
          prop="idCard">
          <ElInput v-model="formData.idCard" />
        </ElFormItem> -->
        <ElFormItem>
          <ElButton
            type="primary"
            @click="() => onSearch()">
            查询
          </ElButton>
          <ElButton @click="onSearch(true)">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </template>
    <template #table="{ maxHeight }">
      <ElTable
        ref="tableRef"
        :max-height="maxHeight"
        :border="true"
        :data="tableData">
        <template #empty>
          <ElEmpty
            :image="EmptyImage"
            :image-size="160"
            description="暂无解析简历" />
        </template>
        <ElTableColumn
          v-if="isTableSelect"
          type="selection"
          width="40" />
        <ElTableColumn label="任务ID">
          <template #default="{ row }">
            <i class="iconfont icon-document" />
            {{ row.bid }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          :show-overflow-tooltip="true"
          prop="taskName"
          label="任务名称" />
        <ElTableColumn
          prop="schedule"
          label="任务进度">
          <template #default="{ row }">
            <ElProgress
              v-if="row.taskStatus == 3"
              :stroke-width="8"
              :percentage="0" />
            <ElProgress
              v-else-if="row.taskStatus != 2"
              :stroke-width="8"
              :striped="true"
              :striped-flow="true"
              :percentage="row.processValue" />
            <ElProgress
              v-else
              :stroke-width="8"
              :percentage="100"
              status="success" />
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="taskStatus"
          label="任务状态">
          <template #default="{ row }">
            {{
              {
                '1': '解析中',
                '2': '解析完成',
                '3': '解析终止',
                '4': '解析异常',
              }[row.taskStatus]
            }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="sourceChannel"
          label="来源渠道" />
        <ElTableColumn
          prop="createdByName"
          label="所属账号" />
        <ElTableColumn
          prop="createdAt"
          label="上传时间" />
        <ElTableColumn label="操作">
          <template #default="{ row }">
            <!-- 终止任务 -->
            <ElTooltip
              content="终止任务"
              placement="top">
              <ElButton
                v-if="[1, 2, 4].includes(row.taskStatus)"
                :disabled="[2, 4].includes(row.taskStatus)"
                :circle="true"
                type="danger"
                @click="onStop(row)">
                <template #icon>
                  <i class="iconfont icon-pause" />
                </template>
              </ElButton>
            </ElTooltip>
            <!-- 启动任务 -->
            <ElTooltip
              content="启动任务"
              placement="top">
              <ElButton
                v-if="![1, 2, 4].includes(row.taskStatus)"
                :circle="true"
                type="success"
                @click="onStart(row)">
                <template #icon>
                  <i class="iconfont icon-play" />
                </template>
              </ElButton>
            </ElTooltip>
            <!-- 查看详情 -->
            <!-- <ElTooltip
              content="查看详情"
              placement="top">
              <ElButton
                circle
                type="info"
                :disabled="[1, 3, 4].includes(row.taskStatus)"
                @click="onDetail(row)">
                <template #icon>
                  <i class="iconfont icon-view" />
                </template>
              </ElButton>
            </ElTooltip> -->
            <ElTooltip
              content="查看详情"
              placement="top">
              <ElButton
                :circle="true"
                type="info"
                :disabled="[1, 3].includes(row.taskStatus)"
                @click="onDetail(row)">
                <template #icon>
                  <i class="iconfont icon-view" />
                </template>
              </ElButton>
            </ElTooltip>
            <!-- 下载原件 -->
            <ElTooltip
              content="下载原件"
              placement="top">
              <ElButton
                :circle="true"
                type="success"
                @click="onDownLoad('single', row)">
                <template #icon>
                  <i class="iconfont icon-download" />
                </template>
              </ElButton>
            </ElTooltip>
            <!-- 删除 -->
            <ElTooltip
              content="删除"
              placement="top">
              <ElButton
                :circle="true"
                type="danger"
                @click="onDelete('single', row)">
                <template #icon>
                  <i class="iconfont icon-delete" />
                </template>
              </ElButton>
            </ElTooltip>
          </template>
        </ElTableColumn>
      </ElTable>
    </template>
    <template #footer-extra>
      <div>
        <ElButton
          v-if="!isTableSelect"
          :link="true"
          @click="isTableSelect = true">
          <div class="flex items-center text-[12px]">
            <i class="iconfont icon-delete text-[12px] text-[#0055FF]" />
            <span class="ml-[5px] text-[#0055FF]">批量下载/删除</span>
          </div>
        </ElButton>
        <template v-else>
          <ElButton
            :link="true"
            @click="onDownLoad('select', selectTableData)">
            <span class="text-[#409EFF]">下载</span>
            <template #icon>
              <i class="iconfont icon-download text-[#409EFF]" />
            </template>
          </ElButton>
          <ElButton
            :link="true"
            type="danger"
            @click="onDelete('select', selectTableData)">
            删除
            <template #icon>
              <i class="iconfont icon-download" />
            </template>
          </ElButton>
          <ElDivider direction="vertical" />
          <ElButton
            :link="true"
            @click="onCancelSelect">
            取消
          </ElButton>
        </template>
      </div>
    </template>
    <template #pagination>
      <ElPagination
        v-model:page-size="pagination.pageSize"
        v-model:current-page="pagination.pageNum"
        layout="total,sizes,prev,pager,next,jumper"
        :total="pagination.total"
        @size-change="() => onSearch()"
        @current-change="() => onSearch()" />
    </template>
  </TablePageLayout>
</template>

<script setup>
  import * as apis from '@/apis/resume'
  import EmptyImage from '@/assets/images/resume/analysis/empty.png'
  import { TablePageLayout } from '@/components'
  import BackTitle from '@/components/back-title/index.vue'
  import { usePagination } from '@/utils/hook'
  import { downloadFilesAsZip } from '@/utils/index'

  const { pagination } = usePagination()
  const router = useRouter()
  const route = useRoute()
  const formData = reactive({
    taskId: '',
    taskStatus: '',
    name: '',
    // idCard: '',
  })
  const tableRef = ref(null)
  const formRef = ref(null)
  const tableData = ref([])

  const selectTableData = ref([])
  const isTableSelect = ref(false)
  onMounted(() => {
    const pageNum = localStorage.getItem('level_two_pageNum')
    const pageSize = localStorage.getItem('level_two_pageSize')
    if (pageNum && pageSize) {
      pagination.pageNum = parseInt(pageNum)
      pagination.pageSize = parseInt(pageSize)
    }
    onSearch()
  })
  const onSearch = async (isReset = false) => {
    try {
      if (isReset) {
        formRef.value.resetFields()
      }
      const res = await apis.getTaskSubTaskPage({
        ...formData,
        ...pagination,
        fatherTaskId: route.query.fatherTaskId,
      })
      const { total, list } = res.data.data
      tableData.value = list
      pagination.total = total
    } catch (error) {
      console.log(error)
    }
  }

  const onCancelSelect = () => {
    selectTableData.value = []
    tableRef.value.clearSelection()
    isTableSelect.value = false
  }

  const onStart = async (row) => {
    try {
      await apis.getTaskStart({ taskId: row.bid })
      ElMessage.success('启动成功')
      onSearch()
    } catch (error) {
      ElMessage.error(error.message)
    }
  }
  const onStop = (row) => {
    ElMessageBox({
      title: '终止任务',
      message: '是否终止当前解析！',
      type: 'error',
      showCancelButton: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = 'Loading...'

          try {
            await apis.getTaskStop({ taskId: row.bid })
            ElMessage.success('终止成功')
            done()
            instance.confirmButtonLoading = false
            onSearch()
          } catch (error) {
            ElMessage.error(error.message)
            instance.confirmButtonLoading = false
          }
        } else {
          done()
        }
      },
    })
  }

  const onDetail = (row) => {
    localStorage.setItem('level_two_pageNum', pagination.pageNum)
    localStorage.setItem('level_two_pageSize', pagination.pageSize)
    router.push({
      path: `/resume/analysis/info`,
      query: {
        resumeBid: row.resumeBid,
        resumeUrl: row.resumeUrl,
      },
    })
  }
  const onDownLoad = (type, row) => {
    const isSingle = type === 'single'
    ElMessageBox({
      title: isSingle ? '下载' : '批量下载',
      message: isSingle ? '是否确认下载当前选中的文档原件' : '是否确认下载所有当前选中的文档原件',
      type: 'warning',
      showCancelButton: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = 'Loading...'

          setTimeout(() => {
            if (isSingle) {
              window.open(row.resumeUrl, '_blank')
            } else {
              if (row.length === 0) {
                instance.confirmButtonText = '确定'
                instance.confirmButtonLoading = false
                ElMessage.error('请先勾选需要下载的文档原件')
                throw new Error('请先勾选需要下载的文档原件')
              }
              const fileList = row.map((item) => {
                return {
                  fileName: item.name,
                  url: item.resumeUrl,
                }
              })
              downloadFilesAsZip(fileList, '简历解析.zip')
            }
            done()
            instance.confirmButtonLoading = false
          }, 2000)
        } else {
          done()
        }
      },
    })
  }
  const onDelete = (type = 'single', row) => {
    const isSingle = type === 'single'
    ElMessageBox({
      title: isSingle ? '确认删除当前选中文档原件？' : '确认删除当前所有选中文档原件？',
      message: isSingle
        ? '提示：删除成功则人才画像处的所有关联简历数据也将同时删除。'
        : '提示：删除成功则人才画像处的所有关联简历数据也将同时删除。',
      type: 'warning',
      showCancelButton: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = 'Loading...'
          try {
            if (isSingle) {
              await apis.deleteTask({ taskId: row.bid })
              ElMessage.success('删除成功')
              onSearch()
            } else {
              if (row.length === 0) {
                instance.confirmButtonText = '确定'
                instance.confirmButtonLoading = false
                ElMessage.error('请先勾选需要删除的文档原件')
                throw new Error('请先勾选需要删除的文档原件')
              }
              const taskIds = row.map((item) => item.bid)
              await apis.deleteBatchTask({ taskIds })
              ElMessage.success('删除成功')
              onCancelSelect()
              onSearch()
            }
            done()
            instance.confirmButtonLoading = false
          } catch (error) {
            ElMessage.error(error)
            instance.confirmButtonText = '确定'
            instance.confirmButtonLoading = false
          }
        } else {
          done()
        }
      },
    })
  }
</script>

<style lang="scss" scoped>
  .pageContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    border-radius: 4px;
    background-color: #fff;

    .head {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        color: #303133;
        font-weight: 600;
        font-size: 20px;
        line-height: 28px;
      }
    }

    .line {
      margin: 12px 0;
      border-bottom: 1px solid #dcdfe6;
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 26px;
    }
  }
</style>
