<template>
  <div class="container">
    <ElTable
      :data="data"
      :border="true">
      <ElTableColumn
        label="序号"
        width="90">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="任务id"
        prop="bid"
        width="200" />
      <ElTableColumn
        label="任务名称"
        prop="taskName" />
      <ElTableColumn
        label="来源渠道"
        prop="sourceChannel"
        width="180" />
      <ElTableColumn
        label="所属账号"
        prop="createdByName"
        width="180" />
      <ElTableColumn
        label="上传时间"
        prop="createdAt"
        width="180" />
      <ElTableColumn
        label="备注"
        prop="remark" />
      <ElTableColumn
        label="操作"
        width="180">
        <template #default="{ row }">
          <ElButton
            :link="true"
            type="primary"
            @click="
              router.push({
                path: `/resume/analysis/info`,
                query: {
                  resumeBid: row.resumeBid,
                  resumeUrl: row.resumeUrl,
                },
              })
            ">
            查看详情
          </ElButton>
          <ElButton
            :link="true"
            type="primary"
            @click="onDownloadFile(row)">
            下载原件
          </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
    <!-- <div class="pagination">
      <ElPagination
        :total="data.length"
        background
        layout="total, prev, pager, next" />
    </div> -->
  </div>
</template>

<script setup>
  const props = defineProps({
    data: {
      type: Array,
      default: () => [],
    },
  })
  const { data } = toRefs(props)

  const router = useRouter()

  const onDownloadFile = (row) => {
    ElMessageBox({
      title: '下载',
      type: 'warning',
      message: '是否确认下载当前选中的文档原件？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          window.open(row.resumeUrl, '_blank')
          done()
        } else {
          done()
        }
      },
    })
  }
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    flex-direction: column;
    // height: 500px;
    margin-top: 12px;

    .pagination {
      display: flex;
      justify-content: end;
      margin-top: 20px;
    }
  }

  :deep(.el-table__header th) {
    background: #f5f7fa;
  }
</style>
