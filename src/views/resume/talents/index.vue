<template>
  <div class="page">
    <div class="searchBox">
      <div class="title">人才画像</div>
      <div class="searchContainer">
        <div class="searchLine">
          <ElInput
            v-model="searchInfo.name"
            :prefix-icon="Search"
            placeholder="搜索人才姓名" />
          <PositionNameSelect
            v-model:position-name="searchInfo.position"
            placeholder="搜索职位名称" />
          <!-- <ElInput
            v-model="searchInfo.idCard"
            :prefix-icon="Search"
            placeholder="搜索身份证号" /> -->
          <ElInput
            v-model="searchInfo.academy"
            :prefix-icon="Search"
            placeholder="搜索毕业院校" />
          <ElInput
            v-model="searchInfo.major"
            :prefix-icon="Search"
            placeholder="搜索学科专业" />
          <ElDatePicker
            v-model="searchInfo.applyDate"
            style="min-width: 220px"
            type="daterange"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期" />
          <div class="flex items-center gap-[8px] text-[14px] text-[#A8ABB2]">
            <ElInput
              v-model="searchInfo.minAge"
              style="width: 70px"
              :min="0"
              type="number" />
            <span>-</span>
            <ElInput
              v-model="searchInfo.maxAge"
              style="width: 70px"
              :min="0"
              type="number" />
            <span>岁</span>
          </div>
          <ElDropdown
            v-for="(item, index) in searchDropdownMenuList.slice(0, isHide ? 1 : 3)"
            :key="index"
            class="dropDown">
            <div class="searchItem">
              <div :class="`searchItemText ${searchInfo[item.name] ? 'searchItemTextActive' : ''}`">
                {{
                  searchInfo[item.name]
                    ? searchInfo[item.name] === '不限'
                      ? '不限' + item.chinese
                      : searchInfo[item.name]
                    : '不限' + item.chinese
                }}
              </div>
              <ElIcon
                :class="`el-icon--right ${searchInfo[item.name] ? 'searchItemTextActive' : ''}`">
                <ArrowDown />
              </ElIcon>
            </div>
            <template #dropdown>
              <ElDropdownMenu>
                <ElDropdownItem
                  v-for="(fieldItem, fieldIndex) in item.value"
                  :key="fieldIndex"
                  @click="onDropdownItemClick(item.name, fieldItem)">
                  {{ fieldItem }}
                </ElDropdownItem>
              </ElDropdownMenu>
            </template>
          </ElDropdown>
          <ElButton
            v-show="isHide"
            :link="true"
            type="primary">
            查询
          </ElButton>
          <ElButton
            v-show="isHide"
            :link="true"
            type="danger"
            @click="onReset">
            重置
          </ElButton>
        </div>
        <div :class="`searchLine ${isHide ? '' : 'isShow'}`">
          <ElCascader
            v-model="searchInfo.workdLocation"
            placeholder="请选择期望工作地点"
            :options="workdLocationOptions"
            :clearable="true" />
          <ElDropdown
            v-for="(item, index) in searchDropdownMenuList.slice(3, searchDropdownMenuList.length)"
            :key="index + 4"
            class="dropDown">
            <div class="searchItem">
              <div :class="`searchItemText ${searchInfo[item.name] ? 'searchItemTextActive' : ''}`">
                {{
                  searchInfo[item.name]
                    ? item.name === 'orderRule'
                      ? searchInfo[item.name]
                      : searchInfo[item.name] === '不限'
                        ? '不限' + item.chinese
                        : searchInfo[item.name]
                    : item.name === 'orderRule'
                      ? item.chinese
                      : '不限' + item.chinese
                }}
              </div>
              <ElIcon
                :class="`el-icon--right ${searchInfo[item.name] ? 'searchItemTextActive' : ''}`">
                <ArrowDown />
              </ElIcon>
            </div>
            <template #dropdown>
              <ElDropdownMenu>
                <ElDropdownItem
                  v-for="(fieldItem, fieldIndex) in item.value"
                  :key="fieldIndex"
                  @click="onDropdownItemClick(item.name, fieldItem)">
                  {{ fieldItem }}
                </ElDropdownItem>
              </ElDropdownMenu>
            </template>
          </ElDropdown>
          <ElButton
            :link="true"
            type="primary"
            @click="onSearch">
            查询
          </ElButton>
          <ElButton
            :link="true"
            type="danger"
            @click="onReset">
            重置
          </ElButton>
        </div>
        <div
          class="toggle"
          @click="toggle">
          <ElIcon
            color="#C0C4CC"
            :size="16"
            :class="isHide ? 'rotate-[90deg]' : 'rotate-[-90deg]'">
            <DArrowRight />
          </ElIcon>
        </div>
      </div>
    </div>
    <ElScrollbar class="scroll_bar">
      <div
        v-loading="loading"
        class="content">
        <div
          v-for="(item, index) in dataList"
          :key="index"
          class="contentBox"
          @click="
            router.push({
              path: '/resume/talents/detail',
              query: { id: item.bid },
            })
          ">
          <div class="talentsInfo">
            <div class="avatar">
              <img
                loading="lazy"
                alt=""
                :src="getDefaultAvatar(item)" />
              <div
                v-if="item.gender === 0"
                class="man bg-[#0055FF]">
                <i class="iconfont icon-man text-[10px]" />
              </div>
              <div
                v-if="item.gender === 1"
                class="woman bg-[#FF7E86]">
                <i class="iconfont icon-woman text-[10px]" />
              </div>
            </div>
            <div class="detailInfo">
              <div class="detailInfoHead">
                <div class="name">
                  <span v-if="item.name">{{ item.name }}</span>
                  <span
                    v-else
                    class="text-[#999]">
                    暂无姓名
                  </span>
                  <span class="age">/{{ item.age }}岁</span>
                </div>
                <img
                  v-if="item.sourceChannel === 'BOSS直聘'"
                  class="source"
                  src="@/assets/images/resume/talents/icon_boss.png"
                  alt="" />
                <img
                  v-else-if="item.sourceChannel === '手动上传'"
                  class="source"
                  src="@/assets/images/resume/talents/icon_manual.png"
                  alt="" />
              </div>
              <div class="infoTag">
                <ElTooltip
                  v-for="(tagItem, tagIndex) in item?.eduTags"
                  :key="tagIndex"
                  placement="top"
                  :content="tagItem">
                  <ElTag
                    v-if="!tagItem?.includes('GPA') && !tagItem?.includes('专业排名')"
                    type="primary"
                    size="small"
                    :plain="true">
                    {{ tagItem.length > 10 ? tagItem.slice(0, 7) + '...' : tagItem }}
                  </ElTag>
                </ElTooltip>
                <ElTooltip
                  v-for="(tagItem, tagIndex) in item?.careerTags"
                  :key="tagIndex"
                  placement="top"
                  :content="tagItem">
                  <ElTag
                    v-if="!tagItem?.includes('GPA') && !tagItem?.includes('专业排名')"
                    type="success"
                    size="small"
                    :plain="true">
                    {{ tagItem.length > 10 ? tagItem.slice(0, 7) + '...' : tagItem }}
                  </ElTag>
                </ElTooltip>
                <!-- <ElTag
                  v-for="(tagItem, tagIndex) in item?.eduTags"
                  :key="tagIndex"
                  type="primary"
                  size="small"
                  plain>
                  {{ tagItem }}
                </ElTag>
                <ElTag
                  v-for="(tagItem, tagIndex) in item?.careerTags"
                  :key="tagIndex"
                  type="success"
                  size="small"
                  plain>
                  {{ tagItem }}
                </ElTag> -->
              </div>
              <div class="flex">
                <div class="education">
                  <i class="iconfont icon-education infoIcon" />
                  <div class="infoText">
                    <div class="infoTextItem">{{ item.academy }}</div>
                    <div class="infoDivider" />
                    <div class="infoTextItem">{{ item.major }}</div>
                    <div class="infoDivider" />
                    <div class="infoTextItem">
                      {{ qualificationnMap[item.qualificationn] }}
                    </div>
                  </div>
                </div>
                <div
                  class="education"
                  style="width: 240px; flex: none">
                  <i class="iconfont icon-ClockCircleFilled infoIcon" />
                  <div class="infoText">
                    <div class="infoTextItem">{{ item.applicationTime || '暂无数据' }}</div>
                  </div>
                </div>
              </div>
              <div class="flex">
                <div
                  class="education"
                  style="margin-top: 8px">
                  <i class="iconfont icon-work infoIcon" />
                  <div class="infoText">
                    <template v-if="item.working">
                      <div class="infoTextItem">{{ item.working }}</div>
                      <div class="infoDivider" />
                      <div class="infoTextItem">{{ item.position }}</div>
                    </template>
                    <template v-else>
                      <div class="infoTextItem">暂无数据</div>
                    </template>
                  </div>
                </div>
                <div
                  class="education"
                  style="width: 240px; flex: none">
                  <i class="iconfont icon-positionFilled infoIcon" />
                  <div class="infoText">
                    <div class="infoTextItem">{{ item.position || '暂无数据' }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="divider" />
          <div class="skillTag">
            <ElTag
              v-for="skill in item.skillTags"
              :key="skill"
              :title="skill"
              type="info">
              {{ skill }}
            </ElTag>
          </div>
          <div class="score">
            <div class="scoreText">人才综合评分</div>
            <div class="scoreNum">{{ item.score || 0 }}</div>
          </div>
        </div>
        <ElEmpty
          v-show="pagination.total === 0 && !loading"
          :image="EmptyImage"
          description="暂无数据"
          class="h-full w-full" />
      </div>
    </ElScrollbar>
    <div class="pagination">
      <ElPagination
        v-model:page-size="pagination.pageSize"
        v-model:current-page="pagination.pageNum"
        :total="pagination.total"
        :background="true"
        layout="total, prev, pager, next" />
    </div>
  </div>
</template>

<script setup>
  import { getTalentList } from '@/apis/resume'
  import female from '@/assets/images/avatar-female.png'
  import male from '@/assets/images/avatar-male.png'
  import EmptyImage from '@/assets/images/resume/talents/empty.png'
  import unkonw from '@/assets/images/unkonw.png'
  import { PositionNameSelect } from '@/components'
  import {
    genderMap,
    orderRuleMap,
    qualificationnApiMap,
    qualificationnMap,
    searchDropdownMenuList,
    workdLocationOptions,
  } from '@/views/resume/talents/config'
  import { ArrowDown, DArrowRight, Search } from '@element-plus/icons-vue'

  const router = useRouter()
  const route = useRoute()

  const loading = ref(false)

  const searchInfo = reactive({
    name: '',
    // idCard: '',
    academy: '',
    major: '',
    gender: '',
    minAge: '',
    maxAge: '',
    schoolTag: '',
    qualificationn: '',
    workExperience: '',
    workdLocation: [],
    workType: '',
    orderRule: '',
    // 新增字段
    position: route.query.position || '',
    applyDate: [],
    sourceChannel: '',
    matchResult: route.query.matchResult || '',
  })

  // 分页
  const pagination = ref({
    pageSize: 50,
    pageNum: 1,
    total: 0,
  })

  const dataList = ref([])

  const isHide = ref(false)

  function toggle() {
    isHide.value = !isHide.value
  }

  const onDropdownItemClick = (field, value) => {
    searchInfo[field] = value
  }

  const onReset = () => {
    for (const key in searchInfo) {
      if (typeof searchInfo[key] === 'string') {
        searchInfo[key] = ''
      } else {
        for (const childKey in searchInfo[key]) {
          searchInfo[key][childKey] = ''
        }
      }
    }
    searchInfo.workdLocation = []
    searchInfo.applyDate = []
    pagination.value = {
      pageSize: 50,
      pageNum: 1,
      total: 0,
    }
    getList()
  }

  function onSearch() {
    pagination.value = {
      pageSize: 50,
      pageNum: 1,
      total: 0,
    }
    getList()
  }

  async function getList() {
    try {
      loading.value = true
      const params = {
        ...searchInfo,
        orderRule: orderRuleMap[searchInfo.orderRule],
        qualificationn: qualificationnApiMap[searchInfo.qualificationn],
        gender: genderMap[searchInfo?.gender],
        workdLocation: searchInfo?.workdLocation?.[1],
        startDate: searchInfo?.applyDate?.[0],
        endDate: searchInfo?.applyDate?.[1],
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
      }
      delete params.applyDate

      const {
        data: { data: res },
      } = await getTalentList(params)
      dataList.value = res.list.map((item) => {
        return {
          ...item,
          eduTags: JSON.parse(item.eduTags || '[]'),
          careerTags: JSON.parse(item.careerTags || '[]'),
          skillTags: JSON.parse(item.skillTags || '[]'),
        }
      })
      console.log('res', res)

      pagination.value.total = res.total
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  watch(
    () => pagination.value.pageNum,
    () => {
      getList()
    },
  )

  function getDefaultAvatar(item) {
    if (item?.avatarUrl?.includes('http') || item?.avatarUrl?.includes('https'))
      return item.avatarUrl
    return item.gender === 0 ? male : item.gender === 1 ? female : unkonw
  }

  onMounted(() => {
    getList(true)
  })
</script>

<style lang="scss" scoped>
  .page {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: 4px;
    background-color: #f7f8fa;

    .searchBox {
      width: 100%;
      margin-bottom: 20px;
      padding: 20px;
      border-radius: 4px;
      background: #ffff;

      .title {
        margin-bottom: 16px;
        color: #303133;
        font-weight: 600;
        font-size: 20px;
      }

      .searchContainer {
        position: relative;

        .searchLine {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 24px;

          .dropDown {
            flex-shrink: 0;

            .searchItem {
              display: flex;
              align-items: center;
              color: #05f;
              font-size: 14px;
            }
          }

          & + .searchLine {
            justify-content: flex-start;
            max-height: 0;
            margin-top: 12px;
            overflow: hidden;

            &.isShow {
              max-height: 100%;
            }
          }
        }

        .toggle {
          display: flex;
          position: absolute;
          bottom: 0;
          left: 50%;
          align-items: center;
          justify-content: center;
          width: 194px;
          height: 24px;
          transform: translateX(-50%) translateY(44px) perspective(0.5em) rotateX(-3deg);
          background-color: #fff;
          cursor: pointer;
        }
      }
    }

    .scroll_bar {
      box-sizing: border-box;
      width: 100%;
      padding: 16px 0;

      .content {
        display: flex;
        flex-wrap: wrap;
        max-height: calc(100% - 148px);
        padding-bottom: 16px;
        overflow: auto;
        gap: 16px;

        .contentBox {
          position: relative;
          width: calc(50% - 8px);
          height: 213px;
          padding: 20px;
          border-radius: 4px;
          background: #fff;
          transition: all 0.2s;

          &:hover {
            box-shadow: 4px 4px 10px 0 rgb(0 0 0 / 10%);
            cursor: pointer;
          }

          .talentsInfo {
            display: flex;

            .avatar {
              position: relative;
              width: 64px;
              height: 64px;
              overflow: hidden;

              // border-radius: 50%;

              .man,
              .woman {
                display: flex;
                position: absolute;
                right: 0;
                bottom: 0;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                transform: translateX(0);
                border-radius: 50%;
                color: #fff;
              }

              // img {
              //   width: 64px;
              //   height: 64px;
              //   border-radius: 50%;
              // }
            }

            .detailInfo {
              flex: 1;
              margin-left: 16px;

              .detailInfoHead {
                display: flex;
                align-items: center;
                margin-bottom: 14px;

                .name {
                  color: #303133;
                  font-weight: 500;
                  font-size: 18px;

                  .age {
                    color: #909399;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 22px;
                  }
                }

                .source {
                  width: 26px;
                  height: 26px;
                  margin-left: 8px;
                  overflow: hidden;
                  border-radius: 50%;
                }
              }

              .infoTag {
                display: flex;
                align-items: center;
                width: 100%;
                overflow: hidden;
                gap: 6px;
              }

              .education {
                display: flex;
                flex: 1;
                align-items: center;
                margin-top: 12px;
                color: #909399;
                font-size: 14px;

                .infoIcon {
                  color: #cdd0d6;
                }

                .infoText {
                  display: flex;
                  align-items: center;
                  margin-left: 12px;
                  gap: 10px;

                  .infoDivider {
                    width: 1px;
                    height: 13px;
                    background: #dcdfe6;
                  }
                }
              }
            }
          }

          .divider {
            height: 1px;
            margin: 16px 0;
            background: #ebeef5;
          }

          .skillTag {
            display: flex;
            overflow: hidden;
            gap: 6px;
          }

          .score {
            display: flex;
            position: absolute;
            top: 0;
            right: 0;
            align-items: center;
            width: 194px;
            height: 36px;
            padding-left: 30px;
            background: url('@/assets/images/talents-score-bg.png') no-repeat;

            .scoreText {
              margin-right: 10px;
              color: #a8abb2;
              font-size: 12px;
            }

            .scoreNum {
              color: #05f;
              font-weight: 600;
              font-size: 28px;
            }
          }
        }
      }
    }

    .pagination {
      display: flex;
      justify-content: end;
    }
  }
</style>
