import cities from '@/assets/json/pc-code.json'

export const searchDropdownMenuList = [
  {
    name: 'gender',
    chinese: '人才性别',
    value: ['不限', '男', '女'],
  },
  {
    name: 'schoolTag',
    chinese: '学校标签',
    value: ['不限', '985', '211', '双一流', '海外留学经历', '双非', '专升本', '中外合作办学'],
  },
  {
    name: 'qualificationn',
    chinese: '学历',
    value: ['不限', '大专以下', '大专', '本科', '硕士', '博士'],
  },
  {
    name: 'workExperience',
    chinese: '工作经验',
    value: ['不限', '应届生', '3年以下', '3-5年', '5-10年', '10年以上'],
  },
  // {
  //   name: 'workdLocation',
  //   chinese: '工作地',
  //   value: ['不限'],
  // },
  {
    name: 'workType',
    chinese: '职位类型',
    value: ['不限', '全职', '校招', '兼职', '实习'],
  },
  {
    name: 'sourceChannel',
    chinese: '来源渠道',
    value: ['不限', 'BOSS直聘', '手动上传'],
  },
  {
    name: 'matchResult',
    chinese: '匹配结果',
    value: ['不限', '匹配', '未匹配'],
  },
  {
    name: 'orderRule',
    chinese: '综合评分排序',
    value: [
      '综合评分降序',
      '教育背景降序',
      '工作能力降序',
      '语言能力降序',
      '荣誉奖励降序',
      '专业能力降序',
    ],
  },
]

export const workdLocationOptions = cities

export const genderMap = {
  男: 0,
  女: 1,
  '': '',
}

export const qualificationnMap = {
  0: '大专以下',
  1: '大专',
  2: '本科',
  3: '硕士',
  4: '博士',
}

export const qualificationnApiMap = {
  大专以下: 0,
  大专: 1,
  本科: 2,
  硕士: 3,
  博士: 4,
}
export const orderRuleMap = {
  综合评分降序: 0,
  教育背景降序: 1,
  工作能力降序: 2,
  语言能力降序: 3,
  荣誉奖励降序: 4,
  专业能力降序: 5,
}
