<template>
  <ElDrawer
    v-model="visible"
    title="候选人详情"
    :size="1000"
    header-class="mb-[0px]!"
    @close="onClose"
    @open="oneOpen">
    <ElRow class="main">
      <ElCol
        :span="17"
        class="left">
        <ElScrollbar v-if="!isEmpty(candidateInfo.resumeDetails)">
          <div class="pr-[20px] pl-[12px]">
            <Resume :info="candidateInfo.resumeDetails" />
          </div>
        </ElScrollbar>
      </ElCol>
      <ElCol
        :span="7"
        class="right">
        <h1 class="mb-[16px] text-[18px]/[26px] font-bold">打招呼情况</h1>
        <ElSpace
          class="w-full"
          direction="vertical"
          :size="12"
          :fill="true">
          <ElRow
            :gutter="8"
            align="middle">
            <ElCol
              :span="9"
              class="text-[14px]/[22px] text-[#606266]">
              匹配值
            </ElCol>
            <ElCol
              :span="15"
              class="text-[20px]/[28px] text-[#0055FF]">
              {{ candidateInfo.matchPercentage }}
            </ElCol>
          </ElRow>
          <ElRow
            :gutter="8"
            align="middle">
            <ElCol
              :span="9"
              class="text-[14px]/[22px] text-[#606266]">
              打招呼时间
            </ElCol>
            <ElCol
              :span="15"
              class="text-[14px]/[22px] text-[#606266]">
              {{ candidateInfo.greetTime }}
            </ElCol>
          </ElRow>
        </ElSpace>
      </ElCol>
    </ElRow>
  </ElDrawer>
</template>

<script setup>
  import { Resume } from '@/components'
  import { isEmpty } from '@/utils/is'

  defineProps({
    // 当前候选人简历信息
    candidateInfo: {
      type: Object,
      default: () => ({}),
    },
  })

  const visible = defineModel('visible', {
    type: Boolean,
    default: false,
  })

  function oneOpen() {
    console.log('open')
  }

  // 关闭dialog时触发事件
  function onClose() {
    console.log('close')
  }

  // // 关闭dialog
  // function closeDialog() {
  //   visible.value = false
  // }
</script>

<style lang="scss" scoped>
  .main {
    height: 100%;
    overflow: hidden;
    .left {
      height: 100%;
      // padding: 0 20px 0 12px;
      overflow: auto;
      border-right: 1px solid #dcdfe6;
    }

    .right {
      position: relative;
      padding: 20px 0 0 20px;

      .resume_status {
        position: absolute;
        top: -30px;
        right: 0px;
        width: 100px;
      }
    }
  }
</style>
