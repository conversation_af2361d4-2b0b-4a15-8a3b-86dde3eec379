<template>
  <div
    class="person"
    @click="$emit('handleView', person)">
    <PersonAvatar
      :avatar-url="personBaseInfo.tiny"
      :gender="personBaseInfo.gender"
      :size="64" />
    <div class="right_info">
      <div class="info_top text-[14px]/[22px] text-[#606266]">
        <ElSpace
          :size="4"
          :spacer="dividerSpacer">
          <ElSpace
            :size="8"
            :spacer="
              h('span', {
                class: 'text-[#DEDFE0] text-[18px]',
                innerHTML: '/',
              })
            ">
            <div class="flex gap-[4px] text-[18px]/[24px] font-bold">
              <h1>{{ person.name }}</h1>
              <SourceChannel source="BOSS直聘" />
            </div>
            <span v-if="!isEmpty(person.wordYear)">{{ person.wordYear }}</span>
          </ElSpace>
          <span v-if="!isEmpty(expectPosition.salaryDesc)">{{ expectPosition.salaryDesc }}</span>
          <span v-if="!isEmpty(expectPosition.positionName)">
            期望职位：{{ expectPosition.positionName }}
          </span>
        </ElSpace>
      </div>
      <div class="flex flex-col gap-[8px] text-[14px]/[22px] text-[#909399]">
        <ElSpace
          v-if="!isEmpty(person.highestEducation)"
          :size="12">
          <i class="iconfont icon-education text-[#CDD0D6]"></i>
          <span class="whitespace-pre">{{ person.highestEducation }}</span>
        </ElSpace>
        <ElSpace
          v-if="!isEmpty(person.workExperience)"
          :size="12">
          <i class="iconfont icon-work text-[#CDD0D6]"></i>
          <span>{{ person.workExperience }}</span>
        </ElSpace>
      </div>
    </div>
    <div
      v-if="!isEmpty(person.matchPercentage)"
      class="score">
      <div class="scoreText">匹配度</div>
      <div class="scoreNum">
        <span>{{ person.matchPercentage || 0 }}</span>
        <!-- <span class="text-[14px]">%</span> -->
      </div>
    </div>
    <!-- 简历状态进行处理 -->
    <!-- <i
      v-if="true"
      class="jianli active iconfont icon-Obtained"></i>
    <i
      v-else
      class="jianli not iconfont icon-Notobtained"></i> -->
  </div>
</template>

<script setup>
  import { PersonAvatar, SourceChannel } from '@/components'
  import { isEmpty } from '@/utils/is'
  import { h } from 'vue'

  const { person } = defineProps({
    person: {
      type: Object,
      default: () => ({}),
    },
  })
  // 查看详情
  defineEmits(['handleView'])

  const dividerSpacer = h(ElDivider, { direction: 'vertical' })

  // 简历详情 因为数据库存的就是带""的字符串
  // Java返回给前端又加一层""，所以需要JSON.parse两次
  const resumeDetail = computed(() => {
    try {
      return JSON.parse(JSON.parse(person.details))
    } catch (error) {
      console.log(error)
      return {}
    }
  })

  // 用户基本信息 拿取头像和性别
  const personBaseInfo = computed(() => {
    const geekDetail = resumeDetail.value?.geekDetail || {}
    return geekDetail?.geekBaseInfo || {}
  })

  // 期望职位对象
  const expectPosition = computed(() => {
    const expect = resumeDetail.value?.showExpectPosition || {}
    return expect
  })
</script>

<style lang="scss" scoped>
  .person {
    display: flex;
    position: relative;
    padding: 20px;
    overflow: hidden;
    gap: 16px;
    border: 1px solid #f0f2f5;
    border-radius: 4px 4px 4px 4px;
    background: #ffffff;
    cursor: pointer;
    &:hover {
      border-color: #0055ff;
      transition: all 0.3s;
    }
    .right_info {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 16px;
    }

    .score {
      display: flex;
      position: absolute;
      top: 0;
      right: 0;
      align-items: center;
      width: 170px;
      height: 42px;
      padding: 0 20px 10px 47px;
      background: url('@/assets/images/talents-score-bg.png') no-repeat;

      .scoreText {
        margin-right: 10px;
        color: #a8abb2;
        font-size: 12px;
      }

      .scoreNum {
        color: #05f;
        font-weight: 600;
        font-size: 28px;
      }
    }

    .jianli {
      position: absolute;
      right: -9px;
      font-size: 100px;
      &.active {
        color: #b3e09c;
      }
      &.not {
        color: #e6e8eb;
      }
    }
  }
</style>
