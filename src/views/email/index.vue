<template>
  <TablePageLayout title="邮箱管理">
    <template #header-extra>
      <ElButton
        :text="true"
        type="primary"
        @click="handleCreate">
        <template #icon>
          <i class="iconfont icon-circle-plus text-[12px]!" />
        </template>
        添加邮箱
      </ElButton>
    </template>
    <template #search>
      <ElForm
        ref="searchFormRef"
        :model="searchData"
        :inline="true"
        :label-width="68">
        <ElFormItem
          label="邮箱名称"
          prop="emailAccount">
          <ElInput
            v-model="searchData.emailAccount"
            style="width: 306px"
            placeholder="请输入邮箱名称" />
        </ElFormItem>
        <!-- 邮箱目的 -->
        <ElFormItem
          label="邮箱目的"
          prop="emailPurpose">
          <ElSelect
            v-model="searchData.emailPurpose"
            style="width: 306px"
            placeholder="请选择邮箱目的"
            :disabled="true">
            <ElOption
              v-for="item in EMAIL_PURPOSE"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton
            type="primary"
            @click="handleSearch">
            查询
          </ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </template>
    <template #table="{ maxHeight }">
      <ElTable
        v-loading="tableLoading"
        :max-height="maxHeight"
        :data="tableData"
        :border="true">
        <template #empty>
          <ElEmpty
            :image-size="160"
            description="暂无邮箱信息" />
        </template>
        <!-- 邮箱账号 -->
        <ElTableColumn
          label="邮箱账号"
          prop="emailAccount" />
        <!-- 发件人姓名 -->
        <ElTableColumn
          label="发件人姓名"
          prop="senderName" />
        <!-- 状态 -->
        <ElTableColumn
          label="状态"
          prop="status">
          <template #default="{ row }">
            <ElTag
              :type="
                {
                  待校验: 'warning', // 待校验
                  校验成功: 'success', // 校验成功
                  校验失败: 'danger', // 校验失败
                }[row.status]
              ">
              {{ getDictLabel('EMAIL_STATUS', row.status) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <!-- 邮箱目的 -->
        <ElTableColumn
          label="邮箱目的"
          prop="emailPurpose">
          <template #default="{ row }">
            {{ row.emailPurpose }}
          </template>
        </ElTableColumn>
        <!-- 关联渠道 -->
        <ElTableColumn
          label="关联渠道"
          prop="relatedChannel">
          <template #default="{ row }">
            {{ row.relatedChannel }}
          </template>
        </ElTableColumn>
        <!-- 账号所属人 -->
        <ElTableColumn
          label="账号所属人"
          prop="createdByName" />
        <!-- 操作 -->
        <ElTableColumn
          label="操作"
          prop="prop_操作">
          <template #default="{ row }">
            <ElButton
              :link="true"
              type="primary"
              @click="handleEdit(row)">
              编辑
            </ElButton>
            <ElButton
              :link="true"
              :disabled="row.status === '校验成功'"
              type="primary"
              :loading="testLoading === row.bid"
              @click="handleTest(row)">
              校验
            </ElButton>
            <ElPopconfirm
              :visible="deletePopConfirmVisible === row.bid"
              title="是否确认删除该邮箱？删除后AI不再自动接收简历"
              width="235px"
              placement="top"
              @confirm="handleDeleteConfirm(row)"
              @cancel="handleDeleteCancel(row)">
              <template #reference>
                <ElButton
                  :link="true"
                  type="danger"
                  :loading="deleteLoading === row.bid"
                  @click="handleDelete(row)">
                  删除
                </ElButton>
              </template>
            </ElPopconfirm>
          </template>
        </ElTableColumn>
      </ElTable>
    </template>
    <template #pagination>
      <ElPagination
        v-model:page-size="paginationData.pageSize"
        v-model:current-page="paginationData.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager,next,jumper"
        :total="paginationData.total"
        @size-change="handlePageSizeChange"
        @current-change="handlePageNumChange" />
    </template>
  </TablePageLayout>

  <EmailDialog
    v-model:visible="dialogData.visible"
    :type="dialogData.type"
    :row="dialogData.row"
    @success="handleSearch" />
</template>

<script setup>
  import { deleteEmail, getEmailList, testEmail } from '@/apis/email'
  import { TablePageLayout } from '@/components'
  import { getDictLabel, useDict } from '@/stores/dict'
  import { default as EmailDialog } from './email-dialog.vue'

  const { EMAIL_PURPOSE } = useDict('EMAIL_PURPOSE', 'EMAIL_STATUS')

  const searchData = reactive({
    emailAccount: '',
    emailPurpose: '',
  })

  watchEffect(() => {
    if (EMAIL_PURPOSE.value.length) {
      searchData.emailPurpose = EMAIL_PURPOSE.value[0].value
    }
  })

  // 查询
  function handleSearch() {
    paginationData.pageNum = 1
    getTableData()
  }

  const searchFormRef = useTemplateRef('searchFormRef')
  // 重置
  function handleReset() {
    searchFormRef.value.resetFields()
  }

  const tableData = ref([])
  const tableLoading = ref(false)
  function getTableData() {
    tableLoading.value = true
    getEmailList({
      ...searchData,
      pageSize: paginationData.pageSize,
      pageNum: paginationData.pageNum,
    })
      .then((data) => {
        tableData.value = data.list
        paginationData.total = data.total
        paginationData.pageNum = data.page
      })
      .finally(() => {
        tableLoading.value = false
      })
  }
  onMounted(() => {
    getTableData()
  })

  const dialogData = reactive({
    visible: false,
    type: 'create',
    row: null,
  })
  function handleCreate() {
    dialogData.visible = true
    dialogData.type = 'create'
    dialogData.row = null
  }

  function handleEdit(row) {
    dialogData.visible = true
    dialogData.type = 'edit'
    dialogData.row = row
  }

  // 校验
  const testLoading = ref(null)
  function handleTest(row) {
    testLoading.value = row.bid
    testEmail({ bid: row.bid })
      .then(() => {
        ElMessage.success('校验成功')
        getTableData()
      })
      .finally(() => {
        testLoading.value = null
      })
  }

  // 删除
  function handleDelete(row) {
    deletePopConfirmVisible.value = row.bid
  }
  // 确认删除
  const deletePopConfirmVisible = ref(null)
  const deleteLoading = ref(null)
  function handleDeleteConfirm(row) {
    deleteLoading.value = row.bid
    deleteEmail({ bid: row.bid })
      .then(() => {
        ElMessage.success('删除成功')
        getTableData()
      })
      .finally(() => {
        deleteLoading.value = null
      })
    deletePopConfirmVisible.value = null
  }
  // 取消删除
  function handleDeleteCancel() {
    deletePopConfirmVisible.value = null
  }

  const paginationData = reactive({
    pageSize: 10,
    pageNum: 1,
    total: 0,
  })
  // 改变每页条数
  function handlePageSizeChange() {
    paginationData.pageNum = 1
    handleSearch()
  }
  // 改变当前页
  function handlePageNumChange() {
    handleSearch()
  }
</script>

<style lang="scss" scoped></style>
