<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="mt-[8px]">
    <AiButton
      :loading="loading"
      size="small"
      @click="handleClick">
      AI 帮写
    </AiButton>

    <ElDialog
      v-model="dialogVisible"
      title="AI 帮写"
      width="800px"
      :append-to-body="true"
      :before-close="handleBeforeClose">
      <ElAlert
        title="AI生成内容支持添加在职位描述中继续修改，或重新生成"
        type="primary"
        :show-icon="true"
        :closable="false" />
      <div class="content">
        <div class="content_tip">
          <img
            class="content_tip_img"
            src="@/assets/images/jobs/icon-robot.png"
            alt="" />
          <p
            v-if="loading"
            class="content_tip_text">
            AI正在为你生成职位描述···
          </p>
          <p
            v-else
            class="content_tip_text">
            AI已生成，可添加到职位描述中继续修改，或重新生成
          </p>
        </div>
        <div
          v-loading="loading"
          class="content_main"
          v-html="formattedAiContent"></div>
        <div
          v-if="(aiContent || aiContentFailed) && !loading"
          class="content_footer">
          <AiButton
            size="small"
            @click="handleRegenerate">
            重新生成
          </AiButton>
        </div>
      </div>
      <template
        v-if="aiContent && !loading && !aiContentFailed"
        #footer>
        <ElButton
          type="primary"
          :plain="true"
          @click="handleConfirm">
          填入内容区
        </ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup>
  import { generateJobDescAndKeywords } from '@/apis/job'
  import { AiButton } from '@/components'
  import { processAiResponse } from '../utils'

  const { positionType, positionName } = defineProps({
    positionType: {
      type: String,
      default: '',
    },
    positionName: {
      type: String,
      default: '',
    },
  })

  const dialogVisible = ref(false)
  const loading = ref(false)
  const aiContent = ref('')
  // 如果生成失败，需要处理
  const aiContentFailed = ref(false)

  const formattedAiContent = computed(() => {
    return aiContent.value.replace(/\n/g, '<br>')
  })

  async function handleBeforeClose(done) {
    if (loading.value) {
      ElMessage.info('AI正在生成中，请稍后关闭')
      return
    }

    // 如果没确认且已经生成了描述
    if (!hasConfirm.value && aiContent.value) {
      try {
        await ElMessageBox.confirm('是否确认关闭？关闭后将不会保留生成的内容', '提示', {
          type: 'warning',
          showCancelButton: true,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
        })

        done()
      } catch (err) {
        console.log(err)
        return
      }
    }

    done()
  }

  // 生成AI内容的函数
  function generateContent(prevContent = '') {
    loading.value = true

    const data = prevContent
      ? {
          option: '3',
          position_description: prevContent,
          job_type: positionType,
          position_name: positionName,
        } // 重新生成
      : {
          option: '1',
          job_type: positionType,
          position_name: positionName,
        } // 首次生成

    generateJobDescAndKeywords(data)
      .then((res) => {
        const result = processAiResponse(res)
        aiContent.value = result?.position_description || ''
        aiContentFailed.value =
          !result?.position_description || typeof result.position_description !== 'string'

        if (aiContentFailed.value) {
          ElMessage.error('生成失败')
        }
      })
      .catch((err) => {
        console.log(err)
        ElMessage.error('生成失败')
        aiContentFailed.value = true
        aiContent.value = ''
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 重新生成内容
  function handleRegenerate() {
    generateContent(aiContent.value)
    aiContent.value = ''
  }

  function handleClick() {
    if (!positionType) {
      ElMessage.warning('请先选择职位类型')
      return
    }

    if (!positionName) {
      ElMessage.warning('请先填写职位名称')
      return
    }

    dialogVisible.value = true
    aiContent.value = ''
    aiContentFailed.value = false
    hasConfirm.value = false

    generateContent()
  }

  const emit = defineEmits(['confirm'])
  // 填入内容
  const hasConfirm = ref(false)
  function handleConfirm() {
    dialogVisible.value = false

    hasConfirm.value = true
    emit('confirm', aiContent.value)
  }
</script>

<style lang="scss" scoped>
  .content {
    display: flex;
    flex-direction: column;
    height: 370px;
    margin-top: 12px;
    padding: 12px;
    border: 1px solid #f4f4f5;
    border-radius: 4px;

    &_tip {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &_img {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }

      &_text {
        background: linear-gradient(58deg, #1749ff 0%, #00e7ff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-fill-color: transparent;
        font-size: 12px;
        line-height: 20px;
      }
    }

    &_main {
      flex: 1;
      overflow: auto;
      color: #606266;
      font-size: 14px;
      line-height: 22px;
    }

    &_footer {
      display: flex;
      margin-top: 12px;
    }
  }
</style>
