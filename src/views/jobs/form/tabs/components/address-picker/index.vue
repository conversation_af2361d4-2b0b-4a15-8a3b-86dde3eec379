<template>
  <div class="w-full">
    <ElInput
      :value="showValue"
      placeholder="请选择工作地点"
      @click="handleEdit" />

    <ElDialog
      v-model="dialogVisible"
      title="请选择工作地点"
      width="680px"
      :append-to-body="true">
      <ElAlert
        title="请填写真实有效地址，若招聘平台查实造假，招聘平台账号将被冻结。"
        type="primary"
        :show-icon="true"
        :closable="false" />
      <div class="mt-[12px]">
        <ElTable
          v-loading="tableLoading"
          :border="true"
          :data="tableData"
          height="250px">
          <template #empty>
            <ElEmpty
              image-size="80px"
              :image="IconAddress">
              <template #description>
                <p class="mt-[-20px]! leading-[24px]">暂无地址，请添加新地址。</p>
              </template>
            </ElEmpty>
          </template>
          <ElTableColumn
            label="工作地点"
            prop="fullAddress" />
          <ElTableColumn
            label="操作"
            width="200">
            <template #default="{ row }">
              <ElButton
                :link="true"
                type="primary"
                @click="handleSelect(row)">
                选择
              </ElButton>
              <ElButton
                :link="true"
                type="primary"
                @click="handleDelete(row)">
                删除
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
      <template #footer>
        <ElButton
          type="primary"
          :plain="true"
          @click="handleAdd">
          <template #icon>
            <i class="iconfont icon-circle-plus1 text-[14px]!" />
          </template>
          添加新地址
        </ElButton>
      </template>
    </ElDialog>

    <AddDialog
      v-model:visible="addDialogVisible"
      @confirm="getAddressList" />
  </div>
</template>

<script setup>
  import { deleteWorkLocation, getWorkLocationList } from '@/apis/job'
  import AddDialog from './add-dialog.vue'
  import IconAddress from '@/assets/images/jobs/icon-address.png'

  const addressBid = defineModel('addressBid', {
    type: String,
    default: '',
  })

  const showValue = computed(() => {
    return tableData.value.find((item) => item.bid === addressBid.value)?.fullAddress || ''
  })

  const tableData = ref([])
  const tableLoading = ref(false)
  function getAddressList() {
    tableLoading.value = true
    getWorkLocationList()
      .then((data) => {
        tableData.value = data

        if (tableData.value.length === 0) {
          addressBid.value = ''
        }
      })
      .finally(() => {
        tableLoading.value = false
      })
  }
  onMounted(() => {
    getAddressList()
  })

  const dialogVisible = ref(false)
  function handleEdit() {
    dialogVisible.value = true
  }

  function handleSelect(row) {
    dialogVisible.value = false
    addressBid.value = row.bid
  }

  function handleDelete(row) {
    ElMessageBox.confirm('是否确认删除该地址？', '提示', {
      type: 'warning',
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true

          try {
            await deleteWorkLocation({ bid: row.bid })
            ElMessage.success('删除成功')
            done()
            instance.confirmButtonLoading = false
            getAddressList()
          } catch (error) {
            ElMessage.error(error.message)
          } finally {
            instance.confirmButtonLoading = false
          }
        } else {
          done()
        }
      },
    })
  }

  const addDialogVisible = ref(false)
  function handleAdd() {
    addDialogVisible.value = true
  }
</script>

<style lang="scss" scoped></style>
